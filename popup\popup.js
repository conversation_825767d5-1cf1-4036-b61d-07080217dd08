document.addEventListener('DOMContentLoaded', () => {
  console.log('AI Smart Reply popup loaded');
  const mainView = document.getElementById('main-view');
  const senderInputView = document.getElementById('sender-input-view');
  const emailListView = document.getElementById('email-list-view');

  const currentEmailBtn = document.getElementById('current-email-btn');
  const readSenderBtn = document.getElementById('read-sender-btn');
  const readLatestBtn = document.getElementById('read-latest-btn');
  const fetchSenderEmailsBtn = document.getElementById('fetch-sender-emails-btn');
  const generateRepliesBtn = document.getElementById('generate-replies-btn');
  const backBtn = document.getElementById('back-btn');

  const senderEmailInput = document.getElementById('sender-email');
  const emailList = document.getElementById('email-list');

  let selectedEmails = [];

  // Helper function to get all folders recursively
  function getAllFolders(folders) {
    let allFolders = [];
    for (const folder of folders) {
      allFolders.push(folder);
      if (folder.subFolders && folder.subFolders.length > 0) {
        allFolders = allFolders.concat(getAllFolders(folder.subFolders));
      }
    }
    return allFolders;
  }

  // Helper function to get the currently open/displayed email with folder context
  async function getCurrentOpenEmail() {
    try {
      // Try to get the currently displayed message from message display tabs
      const tabs = await messenger.tabs.query({ active: true, currentWindow: true });

      for (const tab of tabs) {
        try {
          // Check if this is a message display tab
          const displayedMessages = await messenger.messageDisplay.getDisplayedMessages(tab.id);

          if (displayedMessages && displayedMessages.length > 0) {
            console.log('Found displayed message in tab:', tab.id);
            const message = displayedMessages[0];

            // Get folder information for the message
            const folderInfo = await getMessageFolderInfo(message);
            return { message, folderInfo };
          }
        } catch (tabError) {
          // This tab might not be a message display tab, continue to next
          console.log('Tab', tab.id, 'is not a message display tab');
        }
      }

      // Alternative: Try to get from mail tabs
      try {
        const mailTabs = await messenger.mailTabs.query({ active: true, currentWindow: true });

        for (const mailTab of mailTabs) {
          try {
            const selectedMessages = await messenger.mailTabs.getSelectedMessages(mailTab.id);

            if (selectedMessages && selectedMessages.messages && selectedMessages.messages.length > 0) {
              console.log('Found selected message in mail tab:', mailTab.id);
              const message = selectedMessages.messages[0];

              // Get folder information for the message
              const folderInfo = await getMessageFolderInfo(message);
              return { message, folderInfo };
            }
          } catch (mailTabError) {
            console.log('Error getting selected messages from mail tab:', mailTabError);
          }
        }
      } catch (mailTabsError) {
        console.log('Error querying mail tabs:', mailTabsError);
      }

      console.log('No currently open email found');
      return null;
    } catch (error) {
      console.error('Error getting current open email:', error);
      return null;
    }
  }

  // Helper function to get folder information for a message
  async function getMessageFolderInfo(message) {
    try {
      if (!message.folder) {
        console.warn('Message does not have folder information');
        return { type: 'unknown', name: 'Unknown', isInbox: false, isSent: false };
      }

      const folder = message.folder;
      console.log('Message folder:', folder.name, 'Type:', folder.type);

      // Determine folder type
      const folderName = folder.name.toLowerCase();
      const folderType = folder.type;

      const isInbox = folderType === 'inbox' || folderName.includes('inbox');
      const isSent = folderType === 'sent' || folderName.includes('sent') || folderName.includes('outbox');
      const isDrafts = folderType === 'drafts' || folderName.includes('draft');
      const isTrash = folderType === 'trash' || folderName.includes('trash') || folderName.includes('deleted');

      return {
        type: folderType,
        name: folder.name,
        isInbox,
        isSent,
        isDrafts,
        isTrash,
        path: folder.path || folder.name
      };
    } catch (error) {
      console.error('Error getting folder info:', error);
      return { type: 'unknown', name: 'Unknown', isInbox: false, isSent: false };
    }
  }

  readSenderBtn.addEventListener('click', () => {
    mainView.style.display = 'none';
    senderInputView.style.display = 'block';
  });

  backBtn.addEventListener('click', () => {
    emailListView.style.display = 'none';
    mainView.style.display = 'block';
    selectedEmails = []; // Clear selected emails
  });

  currentEmailBtn.addEventListener('click', async () => {
    try {
      console.log('Getting current open email...');

      // Get the currently displayed message with folder context
      const result = await getCurrentOpenEmail();

      if (!result) {
        alert('No email is currently open. Please open an email first.');
        return;
      }

      const { message: currentMessage, folderInfo } = result;
      console.log('Current email found:', currentMessage.subject);
      console.log('Folder info:', folderInfo);

      // Determine reply type based on folder
      let replyType, replyContext;
      if (folderInfo.isInbox) {
        replyType = 'reply';
        replyContext = 'inbox';
        console.log('📥 Email is from Inbox - generating standard reply');
      } else if (folderInfo.isSent) {
        replyType = 'followup';
        replyContext = 'sent';
        console.log('📤 Email is from Sent folder - generating follow-up');
      } else if (folderInfo.isDrafts) {
        alert('Cannot generate reply for draft emails. Please send the email first or select a received email.');
        return;
      } else if (folderInfo.isTrash) {
        alert('Cannot generate reply for deleted emails. Please select an active email.');
        return;
      } else {
        // For other folders (like custom folders), treat as inbox
        replyType = 'reply';
        replyContext = 'other';
        console.log(`📁 Email is from ${folderInfo.name} folder - generating standard reply`);
      }

      // Get the full message content
      const fullMessage = await getFullMessage(currentMessage.id);
      currentMessage.content = fullMessage;

      // Set tone and language (could be made configurable later)
      const tone = 'formal';
      const language = 'English';

      // Disable button and show progress with context
      currentEmailBtn.disabled = true;
      if (replyType === 'followup') {
        currentEmailBtn.textContent = 'Generating Follow-up...';
      } else {
        currentEmailBtn.textContent = 'Generating Reply...';
      }

      try {
        const response = await messenger.runtime.sendMessage({
          action: 'generateReply',
          email: currentMessage,
          tone,
          language,
          replyType,
          replyContext,
          folderInfo
        });

        if (response && response.success) {
          const successMessage = replyType === 'followup'
            ? 'Follow-up email generated successfully and saved as draft!'
            : 'Reply generated successfully and saved as draft!';
          alert(successMessage);
          console.log(`✅ ${replyType === 'followup' ? 'Follow-up' : 'Reply'} generated for current email:`, currentMessage.subject);
        } else {
          const errorMessage = replyType === 'followup'
            ? 'Failed to generate follow-up: '
            : 'Failed to generate reply: ';
          alert(errorMessage + (response?.error || 'Unknown error'));
          console.error(`❌ ${replyType === 'followup' ? 'Follow-up' : 'Reply'} generation failed:`, response?.error);
        }
      } catch (replyError) {
        console.error('Error generating reply:', replyError);
        alert('Error generating reply: ' + replyError.message);
      }

      // Re-enable button
      currentEmailBtn.disabled = false;
      currentEmailBtn.textContent = 'Current open email';

      // Close popup after successful generation
      if (response && response.success) {
        window.close();
      }

    } catch (error) {
      console.error('Error processing current email:', error);
      alert('Error processing current email: ' + error.message);

      // Re-enable button
      currentEmailBtn.disabled = false;
      currentEmailBtn.textContent = 'Current open email';
    }
  });

  readLatestBtn.addEventListener('click', async () => {
    try {
      // Get the default account and its inbox folder
      const accounts = await messenger.accounts.list();
      if (accounts.length === 0) {
        alert("No email accounts found");
        return;
      }

      let allEmails = [];

      // Search through all accounts' inbox folders
      for (const account of accounts) {
        try {
          // Get the inbox folder for this account
          const inboxFolder = account.folders.find(folder =>
            folder.type === 'inbox' || folder.name.toLowerCase() === 'inbox'
          );

          if (inboxFolder) {
            // Query messages from this inbox folder
            const page = await messenger.messages.list(inboxFolder);
            allEmails = allEmails.concat(page.messages);
          }
        } catch (accountError) {
          console.warn(`Error fetching from account ${account.name}:`, accountError);
        }
      }

      // Sort by date (newest first) and take the first 10
      allEmails.sort((a, b) => new Date(b.date) - new Date(a.date));
      allEmails = allEmails.slice(0, 10);

      if (allEmails.length === 0) {
        alert("No emails found");
        return;
      }

      displayEmails(allEmails);
    } catch (e) {
        console.error("Error fetching latest emails:", e);
        alert("Could not fetch latest emails: " + e.message);
    }
  });

  fetchSenderEmailsBtn.addEventListener('click', async () => {
    const sender = senderEmailInput.value.trim();
    if (!sender) {
      alert("Please enter a sender email address");
      return;
    }

    try {
      // Get all accounts
      const accounts = await messenger.accounts.list();
      if (accounts.length === 0) {
        alert("No email accounts found");
        return;
      }

      let senderEmails = [];

      // Search through all accounts and their folders
      for (const account of accounts) {
        try {
          // Search in all folders of this account
          const folders = await getAllFolders(account.folders);

          for (const folder of folders) {
            try {
              // Use query to search for messages from the specific sender
              const page = await messenger.messages.query({
                folder: folder,
                author: sender
              });

              if (page.messages && page.messages.length > 0) {
                senderEmails = senderEmails.concat(page.messages);
              }
            } catch (folderError) {
              console.warn(`Error searching folder ${folder.name}:`, folderError);
            }
          }
        } catch (accountError) {
          console.warn(`Error searching account ${account.name}:`, accountError);
        }
      }

      if (senderEmails.length === 0) {
        alert(`No emails found from ${sender}`);
        return;
      }

      // Sort by date (newest first)
      senderEmails.sort((a, b) => new Date(b.date) - new Date(a.date));

      displayEmails(senderEmails);
    } catch (e) {
      console.error("Error fetching emails from sender:", e);
      alert("Could not fetch emails from the selected sender: " + e.message);
    }
  });

  function displayEmails(messages) {
    console.log(`Displaying ${messages.length} emails`);
    emailList.innerHTML = '';

    if (messages.length === 0) {
      const noEmailsItem = document.createElement('li');
      noEmailsItem.textContent = 'No emails found';
      emailList.appendChild(noEmailsItem);
    } else {
      messages.forEach(message => {
        const listItem = document.createElement('li');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.dataset.messageId = message.id;
        checkbox.addEventListener('change', (e) => {
          if (e.target.checked) {
            selectedEmails.push(message);
          } else {
            selectedEmails = selectedEmails.filter(m => m.id !== message.id);
          }
        });
        listItem.appendChild(checkbox);

        // Better formatting of email display
        const subject = message.subject || '(No Subject)';
        const author = message.author || '(Unknown Sender)';
        const date = message.date ? new Date(message.date).toLocaleDateString() : '';

        listItem.append(` ${subject} (from: ${author}) ${date ? `- ${date}` : ''}`);
        emailList.appendChild(listItem);
      });
    }

    mainView.style.display = 'none';
    senderInputView.style.display = 'none';
    emailListView.style.display = 'block';
  }

  generateRepliesBtn.addEventListener('click', async () => {
    if (selectedEmails.length === 0) {
      alert('Please select at least one email.');
      return;
    }

    try {
      console.log(`Starting reply generation for ${selectedEmails.length} emails`);

      const tone = 'formal'; // Or get from UI
      const language = 'English'; // Or get from UI

      // Disable the button to prevent multiple clicks
      generateRepliesBtn.disabled = true;
      generateRepliesBtn.textContent = 'Generating Replies...';

      let successCount = 0;
      let errorCount = 0;

      // Process emails sequentially to avoid overwhelming the API
      for (let i = 0; i < selectedEmails.length; i++) {
          const email = selectedEmails[i];
          console.log(`Processing email ${i + 1}/${selectedEmails.length}: ${email.subject}`);

          // Update button text to show progress
          generateRepliesBtn.textContent = `Generating Replies... (${i + 1}/${selectedEmails.length})`;

          try {
            const fullMessage = await getFullMessage(email.id);

            if (!fullMessage || fullMessage.trim() === '') {
              console.warn(`No content found for email: ${email.subject}`);
              errorCount++;
              continue;
            }

            email.content = fullMessage;

            // Send message to background script and wait for response
            console.log(`Sending reply generation request for: ${email.subject}`);
            const response = await messenger.runtime.sendMessage({
              action: 'generateReply',
              email,
              tone,
              language
            });

            if (response && response.success) {
              console.log(`✅ Reply generated successfully for: ${email.subject}`);
              successCount++;
            } else {
              console.error(`❌ Reply generation failed for: ${email.subject}`, response?.error);
              errorCount++;
            }

            // Add a small delay between requests to avoid rate limiting
            if (i < selectedEmails.length - 1) {
              console.log('Waiting 1 second before next request...');
              await new Promise(resolve => setTimeout(resolve, 1000));
            }

          } catch (emailError) {
            console.error(`Error processing email ${email.subject}:`, emailError);
            errorCount++;
          }
      }

      // Show results summary
      const totalProcessed = successCount + errorCount;
      let message = `Reply generation completed!\n\n`;
      message += `✅ Successful: ${successCount}\n`;
      if (errorCount > 0) {
        message += `❌ Failed: ${errorCount}\n`;
      }
      message += `\nTotal processed: ${totalProcessed} of ${selectedEmails.length}`;

      if (successCount > 0) {
        message += `\n\nSuccessful replies have been saved as drafts.`;
      }

      if (errorCount > 0) {
        message += `\n\nCheck the console for error details.`;
      }

      console.log(`Final results: ${successCount} successful, ${errorCount} failed out of ${selectedEmails.length} selected emails`);
      alert(message);
      window.close();
    } catch (error) {
      console.error('Error in reply generation process:', error);
      alert('Error generating replies: ' + error.message);

      // Re-enable the button
      generateRepliesBtn.disabled = false;
      generateRepliesBtn.textContent = 'Generate Replies';
    }
  });

  async function getFullMessage(messageId) {
    try {
      console.log(`Fetching full message for ID: ${messageId}`);
      let fullMessage = await messenger.messages.getFull(messageId);
      let plainTextBody = '';

      function findTextPlain(parts) {
          if (!parts || !Array.isArray(parts)) {
              return null;
          }

          for (const part of parts) {
              if (part.contentType === "text/plain" && part.body) {
                  return part.body;
              }
              if (part.parts) {
                  const result = findTextPlain(part.parts);
                  if (result) return result;
              }
          }
          return null;
      }

      // Try to find plain text content
      if (fullMessage.parts) {
          plainTextBody = findTextPlain(fullMessage.parts);
      }

      // Fallback to other content if no plain text found
      if (!plainTextBody && fullMessage.body) {
          plainTextBody = fullMessage.body;
      }

      // If still no content, try to get basic message info
      if (!plainTextBody) {
          const basicMessage = await messenger.messages.get(messageId);
          plainTextBody = `Subject: ${basicMessage.subject}\nFrom: ${basicMessage.author}\nDate: ${basicMessage.date}`;
      }

      console.log(`Retrieved message content, length: ${plainTextBody.length}`);
      return plainTextBody || 'No content available';
    } catch (error) {
      console.error(`Error fetching full message ${messageId}:`, error);
      return `Error retrieving message content: ${error.message}`;
    }
  }
});