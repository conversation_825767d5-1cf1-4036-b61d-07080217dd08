document.addEventListener('DOMContentLoaded', () => {
  const mainView = document.getElementById('main-view');
  const senderInputView = document.getElementById('sender-input-view');
  const emailListView = document.getElementById('email-list-view');

  const readSenderBtn = document.getElementById('read-sender-btn');
  const readLatestBtn = document.getElementById('read-latest-btn');
  const fetchSenderEmailsBtn = document.getElementById('fetch-sender-emails-btn');
  const generateRepliesBtn = document.getElementById('generate-replies-btn');

  const senderEmailInput = document.getElementById('sender-email');
  const emailList = document.getElementById('email-list');

  let selectedEmails = [];

  readSenderBtn.addEventListener('click', () => {
    mainView.style.display = 'none';
    senderInputView.style.display = 'block';
  });

  readLatestBtn.addEventListener('click', async () => {
    try {
      const page = await messenger.messages.query({ unread: false });
      let allEmails = page.messages.slice(0, 10);
      displayEmails(allEmails);
    } catch (e) {
        console.error(e);
        alert("Could not fetch latest emails");
    }
  });

  fetchSenderEmailsBtn.addEventListener('click', async () => {
    const sender = senderEmailInput.value;
    if (sender) {
      try {
        const page = await messenger.messages.query({ from: sender });
        displayEmails(page.messages);
      } catch (e) {
        console.error(e);
        alert("Could not fetch emails from the selected sender");
      }
    }
  });

  function displayEmails(messages) {
    emailList.innerHTML = '';
    messages.forEach(message => {
      const listItem = document.createElement('li');
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.dataset.messageId = message.id;
      checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
          selectedEmails.push(message);
        } else {
          selectedEmails = selectedEmails.filter(m => m.id !== message.id);
        }
      });
      listItem.appendChild(checkbox);
      listItem.append(` ${message.subject} (from: ${message.author})`);
      emailList.appendChild(listItem);
    });

    mainView.style.display = 'none';
    senderInputView.style.display = 'none';
    emailListView.style.display = 'block';
  }

  generateRepliesBtn.addEventListener('click', async () => {
    if (selectedEmails.length === 0) {
      alert('Please select at least one email.');
      return;
    }

    const tone = 'formal'; // Or get from UI
    const language = 'English'; // Or get from UI

    for (const email of selectedEmails) {
        const fullMessage = await getFullMessage(email.id);
        email.content = fullMessage;
        messenger.runtime.sendMessage({ action: 'generateReply', email, tone, language });
    }

    alert('Replies are being generated and will be saved as drafts.');
    window.close();
  });

  async function getFullMessage(messageId) {
    let fullMessage = await messenger.messages.getFull(messageId);
    let plainTextBody = '';

    function findTextPlain(parts) {
        for (const part of parts) {
            if (part.contentType === "text/plain") {
                return part.body;
            }
            if (part.parts) {
                const result = findTextPlain(part.parts);
                if (result) return result;
            }
        }
        return null;
    }

    plainTextBody = findTextPlain(fullMessage.parts);

    return plainTextBody || fullMessage.body || ''; // Fallback
  }
});