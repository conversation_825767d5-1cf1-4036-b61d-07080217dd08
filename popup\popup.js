document.addEventListener('DOMContentLoaded', () => {
  console.log('AI Smart Reply popup loaded');

  // Get references to UI elements
  const llmInput = document.getElementById('llm-input');
  const modelInput = document.getElementById('model-input');
  const fetchModelsBtn = document.getElementById('fetch-models-btn');
  const saveConfigBtn = document.getElementById('save-config-btn');
  
  const specifySenderCheckbox = document.getElementById('specify-sender-checkbox');
  const senderInput = document.getElementById('sender-input');
  const latestEmailsCheckbox = document.getElementById('latest-emails-checkbox');
  const currentEmailCheckbox = document.getElementById('current-email-checkbox');
  const fetchEmailsBtn = document.getElementById('fetch-emails-btn');
  const draftReplyBtn = document.getElementById('draft-reply-btn');
  
  const emailList = document.getElementById('email-list');
  const draftReplySelectedBtn = document.getElementById('draft-reply-selected-btn');
  
  const progressBar = document.getElementById('progress-bar');

  // State management
  let selectedEmails = [];
  let allEmails = [];
  let currentConfig = {};

  // Initialize the popup
  initializePopup();

  // Initialize popup
  async function initializePopup() {
    await loadConfiguration();
    setupEventListeners();
    populateEmailList();
  }

  // Load current configuration
  async function loadConfiguration() {
    try {
      const config = await messenger.storage.local.get([
        'llmProvider', 'llmModel', 'apiKey'
      ]);
      
      currentConfig = {
        provider: config.llmProvider || '',
        model: config.llmModel || '',
        hasApiKey: !!config.apiKey
      };

      // Set UI values
      if (currentConfig.provider) {
        llmInput.value = currentConfig.provider;
      }
      if (currentConfig.model) {
        modelInput.value = currentConfig.model;
      }

      console.log('Configuration loaded:', currentConfig);
    } catch (error) {
      console.error('Error loading configuration:', error);
    }
  }

  // Setup event listeners
  function setupEventListeners() {
    fetchModelsBtn.addEventListener('click', () => alert('Fetching models...'));
    saveConfigBtn.addEventListener('click', saveConfiguration);
    fetchEmailsBtn.addEventListener('click', fetchEmails);
    draftReplyBtn.addEventListener('click', handleDraftReply);
    draftReplySelectedBtn.addEventListener('click', handleDraftSelectedReplies);

    specifySenderCheckbox.addEventListener('change', () => {
      senderInput.disabled = !specifySenderCheckbox.checked;
    });
  }

  // Save configuration
  async function saveConfiguration() {
    const provider = llmInput.value;
    const model = modelInput.value;

    if (!provider || !model) {
      alert('Please enter both LLM provider and model');
      return;
    }

    try {
      await messenger.storage.local.set({
        llmProvider: provider,
        llmModel: model
      });

      currentConfig.provider = provider;
      currentConfig.model = model;

      alert('Configuration saved successfully!');
      console.log('Configuration saved:', { provider, model });
    } catch (error) {
      console.error('Error saving configuration:', error);
      alert('Error saving configuration: ' + error.message);
    }
  }

  // Fetch emails
  async function fetchEmails() {
    fetchEmailsBtn.disabled = true;
    fetchEmailsBtn.textContent = 'Fetching...';
    updateProgress(10);

    try {
      if (currentEmailCheckbox.checked) {
        await fetchCurrentEmail();
      } else if (latestEmailsCheckbox.checked) {
        const senderEmail = senderInput.value.trim();
        if (!senderEmail && specifySenderCheckbox.checked) {
          alert('Please enter a sender email address');
          return;
        }
        await fetchLatestEmails(senderEmail);
      }
    } catch (error) {
      console.error('Error fetching emails:', error);
      alert('Error fetching emails: ' + error.message);
    } finally {
      fetchEmailsBtn.disabled = false;
      fetchEmailsBtn.textContent = 'Fetch Emails';
      updateProgress(0);
    }
  }

  // Fetch current open email
  async function fetchCurrentEmail() {
    try {
      const result = await getCurrentOpenEmail();
      
      if (!result) {
        alert('No email is currently open. Please open an email first.');
        return;
      }

      allEmails = [{ message: result.message, folderInfo: result.folderInfo }];
      populateEmailList();
      updateProgress(100);
      
      console.log('Current email fetched:', result.message.subject);
    } catch (error) {
      console.error('Error fetching current email:', error);
      throw error;
    }
  }

  // Fetch latest emails from sender
  async function fetchLatestEmails(senderEmail) {
    try {
      updateProgress(30);
      
      const accounts = await messenger.accounts.list();
      let foundEmails = [];
      
      updateProgress(50);
      
      for (const account of accounts) {
        const folders = getAllFolders(account.folders);
        
        for (const folder of folders) {
          try {
            const messages = await messenger.messages.list(folder);
            const senderMessages = messages.messages.filter(msg => 
              !senderEmail || (msg.author && msg.author.toLowerCase().includes(senderEmail.toLowerCase()))
            );
            
            foundEmails = foundEmails.concat(senderMessages);
          } catch (folderError) {
            console.log('Error accessing folder:', folder.name, folderError);
          }
        }
      }
      
      updateProgress(80);
      
      foundEmails.sort((a, b) => new Date(b.date) - new Date(a.date));
      allEmails = foundEmails.slice(0, 10);
      
      populateEmailList();
      updateProgress(100);
      
      console.log(`Found ${allEmails.length} emails`);
    } catch (error) {
      console.error('Error fetching latest emails:', error);
      throw error;
    }
  }

  // Get all folders recursively
  function getAllFolders(folders) {
    let allFolders = [];
    for (const folder of folders) {
      allFolders.push(folder);
      if (folder.subFolders && folder.subFolders.length > 0) {
        allFolders = allFolders.concat(getAllFolders(folder.subFolders));
      }
    }
    return allFolders;
  }

  // Populate email list
  function populateEmailList() {
    emailList.innerHTML = '';

    if (allEmails.length === 0) {
      const emptyItem = document.createElement('li');
      emptyItem.innerHTML = '<label>No emails found</label>';
      emailList.appendChild(emptyItem);
      return;
    }

    allEmails.forEach((emailData, index) => {
      const listItem = document.createElement('li');

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = `email-${index}`;
      checkbox.value = index;
      checkbox.addEventListener('change', updateSelectedEmails);

      const label = document.createElement('label');
      label.htmlFor = `email-${index}`;
      label.textContent = emailData.message.subject || 'No subject';

      listItem.appendChild(checkbox);
      listItem.appendChild(label);
      emailList.appendChild(listItem);
    });
  }

  // Update selected emails
  function updateSelectedEmails() {
    const checkboxes = emailList.querySelectorAll('input[type="checkbox"]:checked');
    selectedEmails = Array.from(checkboxes).map(cb => parseInt(cb.value));

    draftReplySelectedBtn.disabled = selectedEmails.length === 0;
    console.log('Selected emails:', selectedEmails.length);
  }

  // Handle draft reply
  async function handleDraftReply() {
    if (allEmails.length === 0) {
      alert('Please fetch emails first');
      return;
    }
    await generateReplyForEmail(allEmails[0].message, allEmails[0].folderInfo);
  }

  // Handle draft reply for selected emails
  async function handleDraftSelectedReplies() {
    if (selectedEmails.length === 0) {
      alert('Please select at least one email');
      return;
    }

    draftReplySelectedBtn.disabled = true;
    draftReplySelectedBtn.textContent = 'Generating...';

    try {
      for (let i = 0; i < selectedEmails.length; i++) {
        const emailIndex = selectedEmails[i];
        const emailData = allEmails[emailIndex];

        updateProgress((i / selectedEmails.length) * 100);
        await generateReplyForEmail(emailData.message, emailData.folderInfo);

        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      alert(`Generated ${selectedEmails.length} replies successfully!`);
    } catch (error) {
      console.error('Error generating replies:', error);
      alert('Error generating replies: ' + error.message);
    } finally {
      draftReplySelectedBtn.disabled = false;
      draftReplySelectedBtn.textContent = 'Draft Reply';
      updateProgress(0);
    }
  }

  // Generate reply for a specific email
  async function generateReplyForEmail(email, folderInfo) {
    try {
      draftReplyBtn.disabled = true;
      draftReplyBtn.textContent = 'Generating...';

      const fullMessage = await getFullMessage(email.id);
      email.content = fullMessage;

      const replyType = folderInfo && (folderInfo.type === 'sent' || folderInfo.type === 'drafts') ? 'followup' : 'reply';

      const response = await messenger.runtime.sendMessage({
        action: 'generateReply',
        email: email,
        tone: 'formal',
        language: 'English',
        replyType: replyType,
        folderInfo: folderInfo
      });

      if (!response || !response.success) {
        throw new Error(response?.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error generating reply:', error);
      throw error;
    } finally {
      draftReplyBtn.disabled = false;
      draftReplyBtn.textContent = 'Draft Reply';
    }
  }

  // Update progress bar
  function updateProgress(percentage) {
    progressBar.style.width = `${percentage}%`;
  }

  // Helper functions from background.js
  async function getCurrentOpenEmail() {
    try {
      const tabs = await messenger.tabs.query({ active: true, currentWindow: true });
      for (const tab of tabs) {
        try {
          const displayedMessages = await messenger.messageDisplay.getDisplayedMessages(tab.id);
          if (displayedMessages && displayedMessages.length > 0) {
            const message = displayedMessages[0];
            const folderInfo = await getMessageFolderInfo(message);
            return { message, folderInfo };
          }
        } catch (tabError) {
          console.log('Tab', tab.id, 'is not a message display tab');
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting current open email:', error);
      return null;
    }
  }

  async function getMessageFolderInfo(message) {
    try {
      if (!message.folder) return { type: 'unknown' };
      return { type: message.folder.type };
    } catch (error) {
      console.error('Error getting folder info:', error);
      return { type: 'unknown' };
    }
  }

  async function getFullMessage(messageId) {
    try {
      const fullMessage = await messenger.messages.getFull(messageId);
      if (!fullMessage) return '';
      let content = '';
      if (fullMessage.parts) {
        content = extractTextFromParts(fullMessage.parts);
      } else if (fullMessage.body) {
        content = fullMessage.body;
      }
      return cleanEmailContent(content);
    } catch (error) {
      console.error('Error getting full message:', error);
      return '';
    }
  }

  function extractTextFromParts(parts) {
    let text = '';
    for (const part of parts) {
      if (part.contentType === 'text/plain' && part.body) {
        text += part.body + '\n';
      } else if (part.contentType === 'text/html' && part.body) {
        const htmlText = part.body.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        text += htmlText + '\n';
      } else if (part.parts) {
        text += extractTextFromParts(part.parts);
      }
    }
    return text;
  }

  function cleanEmailContent(content) {
    if (!content) return '';
    return content.replace(/\s+/g, ' ').trim();
  }
});