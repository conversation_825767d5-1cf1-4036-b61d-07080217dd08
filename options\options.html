<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css"/>
</head>
<body>
  <h2>LLM Configuration</h2>
  <form id="config-form">
    <div>
      <label for="provider">LLM Provider:</label>
      <select id="provider">
        <option value="openai">OpenAI</option>
        <option value="anthropic">Anthropic</option>
        <option value="mistral">Mistral</option>
        <option value="gemini">Gemini</option>
        <option value="deepseek">Deepseek</option>
        <option value="openrouter">OpenRouter</option>
        <option value="custom">Custom</option>
      </select>
    </div>
    <div id="custom-provider-url-container" style="display: none;">
        <label for="custom-provider-url">Custom Provider URL:</label>
        <input type="text" id="custom-provider-url" placeholder="Enter custom provider URL">
    </div>
    <div>
      <label for="api-key">API Key:</label>
      <input type="password" id="api-key" required>
    </div>
    <div>
      <label for="model">Model:</label>
      <input type="text" id="model" placeholder="e.g., gpt-4">
    </div>
        <div>
      <label for="edit-drafts">Edit drafts before saving:</label>
      <input type="checkbox" id="edit-drafts">
    </div>
    <button type="submit">Save Configuration</button>
  </form>
  <script src="options.js"></script>
</body>
</html>