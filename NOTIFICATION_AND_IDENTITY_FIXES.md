# Notification Error & Multi-Account Identity Fixes

## Issues Fixed

### 1. Notification Error Fix
**Problem**: "Property 'buttons' is unsupported by Firefox for notifications.create"

**Root Cause**: Thunderbird/Firefox doesn't support the `buttons` property in notifications.

**Solution**: Removed the unsupported `buttons` property from notification creation.

```javascript
// BEFORE (causing error):
const notificationId = await messenger.notifications.create({
    "type": "basic",
    "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
    "title": "Error",
    "message": `Failed to generate reply for "${email.subject}".`,
    "buttons": [{ "title": "Retry" }]  // ❌ Unsupported
});

// AFTER (fixed):
const notificationId = await messenger.notifications.create({
    "type": "basic",
    "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
    "title": "Error",
    "message": `Failed to generate reply for "${email.subject}". Check console for details.`
    // ✅ No buttons property
});
```

### 2. Enhanced Multi-Account Identity Detection

**Problem**: Drafts still saving to default account instead of correct account.

**Enhanced Solution**: Added multiple detection methods with better debugging.

#### Method 1: Direct Account ID Lookup
```javascript
if (email.folder && email.folder.accountId) {
    const account = await messenger.accounts.get(email.folder.accountId);
    const defaultIdentity = await messenger.identities.getDefault(account.id);
    return defaultIdentity; // ✅ Most reliable method
}
```

#### Method 2: Path-Based Detection
```javascript
if (email.folder && email.folder.path) {
    const accounts = await messenger.accounts.list();
    
    for (const account of accounts) {
        if (email.folder.path.includes(account.name) || 
            email.folder.path.includes(account.id) ||
            email.folder.path.includes(account.identities[0].email)) {
            
            const defaultIdentity = await messenger.identities.getDefault(account.id);
            return defaultIdentity; // ✅ Fallback method
        }
    }
}
```

#### Method 3: Improved Folder Matching
```javascript
async function isFolderInAccount(folder, account) {
    // Check accountId match
    if (folder.accountId && folder.accountId === account.id) {
        return true;
    }
    
    // Check path patterns
    if (folder.path && folder.path.includes(account.name)) {
        return true;
    }
    
    // Check folder hierarchy
    const accountFolders = await getAllAccountFolders(account.folders);
    return accountFolders.some(f => 
        f.id === folder.id || 
        f.path === folder.path || 
        (f.name === folder.name && f.type === folder.type)
    );
}
```

## Debugging Steps

### Step 1: Check Console Output
When generating replies, check the browser console (F12) for detailed logs:

```
Determining correct identity for email...
Email folder has accountId: account123
Found account: <EMAIL>
✅ Method 1 success - Using account default identity: <EMAIL>
Using identity: <EMAIL>
Setting identity to: <EMAIL> (ID: identity456)
```

### Step 2: Run Debug Script
Copy and paste the content of `debug-multi-account.js` into the browser console to get detailed information about:
- Available accounts
- Available identities  
- Current email folder information
- Identity detection process

### Step 3: Check Email Folder Information
Look for this debug output in console:
```
Email debug info: {
    subject: "Test Email",
    author: "<EMAIL>",
    recipients: ["<EMAIL>"],
    folder: {
        name: "Inbox",
        type: "inbox", 
        accountId: "account123",
        path: "/<EMAIL>/Inbox"
    }
}
```

## Expected Behavior After Fix

### Scenario 1: <EMAIL> Inbox Email
1. **Open email** from <EMAIL> inbox
2. **Generate reply** using "Current open email"
3. **Console shows**: "✅ Method 1 success - Using account default identity: <EMAIL>"
4. **Result**: Draft <NAME_EMAIL> drafts with correct "From" field

### Scenario 2: <EMAIL> Sent Email  
1. **Open email** from <EMAIL> sent folder
2. **Generate follow-up** using "Current open email"
3. **Console shows**: "✅ Method 1 success - Using account default identity: <EMAIL>"
4. **Result**: Follow-up draft <NAME_EMAIL> drafts with correct "From" field

## Troubleshooting

### If Identity Detection Still Fails:

1. **Check Permissions**: Ensure `accountsRead` permission is granted
2. **Check Account Setup**: Verify accounts are properly configured in Thunderbird
3. **Check Console Logs**: Look for error messages in identity detection
4. **Run Debug Script**: Use the debug script to see detailed account/identity information

### Common Issues:

#### Issue: "Error getting identities list"
**Solution**: Check if `messenger.identities` API is available. Some Thunderbird versions might have different API access.

#### Issue: "No accountId in folder"
**Solution**: The fallback path-based detection should handle this. Check if folder.path contains account information.

#### Issue: "Folder does not belong to this account"
**Solution**: The folder matching logic will try multiple methods. Check console for detailed matching attempts.

## Testing Instructions

### Test 1: Notification Error Fix
1. **Trigger an error** (e.g., no API key configured)
2. **Check**: No "buttons unsupported" error should appear
3. **Verify**: Simple notification appears without buttons

### Test 2: Multi-Account Identity
1. **Setup**: Configure 2+ email accounts in Thunderbird
2. **Test user2 inbox**: Open <NAME_EMAIL> inbox → Generate reply
3. **Verify**: Draft shows "From: <EMAIL>" and saves to user2 drafts
4. **Test user1 inbox**: Open <NAME_EMAIL> inbox → Generate reply  
5. **Verify**: Draft shows "From: <EMAIL>" and saves to user1 drafts

### Test 3: Debug Information
1. **Open any email**
2. **Open browser console** (F12)
3. **Run debug script** from `debug-multi-account.js`
4. **Review output** for account/identity information

The enhanced identity detection should now properly handle multi-account setups and save drafts to the correct account!
