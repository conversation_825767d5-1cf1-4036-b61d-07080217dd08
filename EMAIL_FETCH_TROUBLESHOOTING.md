# Email Fetching Troubleshooting Guide

## Issues Fixed

The email fetching functionality in the Thunderbird Add-on had several issues that have been resolved:

### 1. **Wrong Query Parameter for Latest Emails**
- **Problem**: The code was using `{ unread: false }` which fetches only READ emails
- **Fix**: Changed to use `messenger.messages.list()` with inbox folders to get all messages, then sort by date

### 2. **Missing Folder Specification**
- **Problem**: Queries didn't specify which folder to search in
- **Fix**: Added proper folder traversal and specified folders in queries

### 3. **Improved Error Handling**
- **Problem**: Basic error handling with generic alerts
- **Fix**: Added detailed error logging and better user feedback

### 4. **Better Message Content Retrieval**
- **Problem**: Simple content extraction that could fail
- **Fix**: Enhanced `getFullMessage()` with fallback mechanisms

## How to Test

### 1. Load the Add-on
1. Open Thunderbird
2. Go to Tools > Add-ons and Themes
3. Click the gear icon > Install Add-on From File
4. Select the add-on folder or zip file

### 2. Test Latest Emails
1. Click the add-on icon in the toolbar
2. Click "Read latest 10 emails"
3. Check browser console (F12) for debug messages
4. Verify emails are displayed with subject, sender, and date

### 3. Test Sender-Specific Emails
1. Click "Read emails from a specific sender"
2. Enter a sender's email address
3. Click "Fetch Emails"
4. Verify emails from that sender are displayed

### 4. Debug Mode
To enable detailed debugging:
1. Open the popup
2. Press F12 to open developer tools
3. Go to Console tab
4. Copy and paste the content of `test-email-fetch.js` into the console
5. Press Enter to run the test

## Common Issues and Solutions

### Issue: "No email accounts found"
- **Cause**: Add-on can't access Thunderbird accounts
- **Solution**: Check that the `accountsRead` permission is in manifest.json

### Issue: "No emails found"
- **Cause**: Empty inbox or permission issues
- **Solution**: 
  1. Verify you have emails in your inbox
  2. Check console for detailed error messages
  3. Ensure `messagesRead` permission is granted

### Issue: "Could not fetch emails from the selected sender"
- **Cause**: Sender email not found or search issues
- **Solution**:
  1. Verify the sender email address is correct
  2. Check if emails from that sender exist
  3. Try with a partial email address

### Issue: Email content is empty when generating replies
- **Cause**: Message content retrieval failing
- **Solution**: Check the enhanced `getFullMessage()` function logs

## Technical Details

### Key Changes Made:

1. **popup/popup.js**:
   - Fixed `readLatestBtn` to use proper folder traversal
   - Enhanced `fetchSenderEmailsBtn` with better search logic
   - Added `getAllFolders()` helper function
   - Improved `displayEmails()` with better formatting
   - Enhanced `getFullMessage()` with robust error handling

2. **background.js**:
   - Removed duplicate code in LLM provider switch statement

3. **popup/popup.html**:
   - Added back button for better navigation

### API Usage:
- `messenger.accounts.list()`: Get all email accounts
- `messenger.messages.list(folder)`: List messages in a specific folder
- `messenger.messages.query({folder, author})`: Search for messages by criteria
- `messenger.messages.getFull(messageId)`: Get full message content

## Permissions Required

The following permissions are needed in manifest.json:
- `accountsRead`: Access to email accounts
- `messagesRead`: Read email messages
- `storage`: Store configuration
- `compose`: Create draft replies
- `notifications`: Show status notifications
- `tabs`: Open editor tabs

## Next Steps

If issues persist:
1. Check Thunderbird's Error Console (Tools > Developer Tools > Error Console)
2. Verify add-on permissions are granted
3. Test with different email accounts/folders
4. Check if Thunderbird version is compatible with WebExtensions API
