document.addEventListener('DOMContentLoaded', () => {
  const configForm = document.getElementById('config-form');
  const providerSelect = document.getElementById('provider');
  const customProviderUrlContainer = document.getElementById('custom-provider-url-container');
  const customProviderUrlInput = document.getElementById('custom-provider-url');
  const apiKeyInput = document.getElementById('api-key');
  const modelInput = document.getElementById('model');
  const editDraftsCheckbox = document.getElementById('edit-drafts');

  // Load saved configuration
  browser.storage.local.get(['llmProvider', 'apiKey', 'llmModel', 'customProviderUrl']).then(result => {
    if (result.llmProvider) {
      providerSelect.value = result.llmProvider;
    }
    if (result.apiKey) {
      apiKeyInput.value = result.apiKey;
    }
    if (result.llmModel) {
      modelInput.value = result.llmModel;
    }
    if (result.customProviderUrl) {
        customProviderUrlInput.value = result.customProviderUrl;
    }
    if (result.editDraftsBeforeSaving) {
      editDraftsCheckbox.checked = result.editDraftsBeforeSaving;
    }
        customProviderUrlInput.value = result.customProviderUrl;
    }

    // Show/hide custom URL field based on loaded provider
    if (providerSelect.value === 'custom') {
      customProviderUrlContainer.style.display = 'block';
    } else {
      customProviderUrlContainer.style.display = 'none';
    }
  });

  // Show/hide custom URL field on provider change
  providerSelect.addEventListener('change', () => {
    if (providerSelect.value === 'custom') {
      customProviderUrlContainer.style.display = 'block';
    } else {
      customProviderUrlContainer.style.display = 'none';
    }
  });

  // Save configuration
  configForm.addEventListener('submit', e => {
    e.preventDefault();
    const provider = providerSelect.value;
    const apiKey = apiKeyInput.value;
    const model = modelInput.value;
    const customProviderUrl = customProviderUrlInput.value;
    const editDrafts = editDraftsCheckbox.checked;

    browser.storage.local.set({
      llmProvider: provider,
      apiKey: apiKey,
      llmModel: model,
      customProviderUrl: customProviderUrl,
      editDraftsBeforeSaving: editDrafts
    }).then(() => {
      // Optionally, show a success message
      alert('Configuration saved!');
    }).catch(err => {
      console.error('Error saving configuration:', err);
      alert('Error saving configuration. See console for details.');
    });
  });
});