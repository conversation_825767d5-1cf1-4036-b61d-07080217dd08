# Smart Context-Aware Reply Generation

## Overview
Enhanced the "Current open email" feature to intelligently detect whether the email is from Inbox (received) or Sent Mail (sent) and generate contextually appropriate responses:

- **📥 Inbox emails** → Generate standard **reply**
- **📤 Sent emails** → Generate **follow-up** email

## How It Works

### 1. Folder Detection
The add-on automatically detects which folder the current email is from:

```javascript
async function getMessageFolderInfo(message) {
    const folder = message.folder;
    const folderName = folder.name.toLowerCase();
    const folderType = folder.type;
    
    const isInbox = folderType === 'inbox' || folderName.includes('inbox');
    const isSent = folderType === 'sent' || folderName.includes('sent');
    const isDrafts = folderType === 'drafts' || folderName.includes('draft');
    const isTrash = folderType === 'trash' || folderName.includes('trash');
    
    return { isInbox, isSent, isDrafts, isTrash, name: folder.name };
}
```

### 2. Context-Aware Processing

#### 📥 **Inbox Email (Received)**
- **Action**: Generate standard reply
- **Button Text**: "Generating Reply..."
- **Subject**: "Re: [Original Subject]"
- **Recipient**: Original sender
- **Prompt**: "Generate a [tone] reply in [language] for the following email I received..."

#### 📤 **Sent Email (Previously Sent)**
- **Action**: Generate follow-up email
- **Button Text**: "Generating Follow-up..."
- **Subject**: "Follow-up: [Original Subject]"
- **Recipient**: Original recipients
- **Prompt**: Specialized follow-up prompt (see below)

#### 📝 **Draft Email**
- **Action**: Show error message
- **Message**: "Cannot generate reply for draft emails. Please send the email first or select a received email."

#### 🗑️ **Trash/Deleted Email**
- **Action**: Show error message
- **Message**: "Cannot generate reply for deleted emails. Please select an active email."

### 3. Specialized Follow-up Prompt

For sent emails, the AI receives this enhanced prompt:

```
Generate a [tone] follow-up email in [language] for the following email that I previously sent. The follow-up should:
- Check on the status or response to my original email
- Be polite and professional
- Not be pushy or aggressive
- Include a gentle reminder if appropriate
- Offer additional assistance or clarification if needed

Original email I sent:
[EMAIL CONTENT]

Generate a follow-up email:
```

## User Experience

### Scenario 1: Reply to Received Email
1. **User opens email from Inbox**
2. **Clicks "Current open email"**
3. **Sees**: "Generating Reply..."
4. **Gets**: Standard reply draft with "Re: [Subject]"

### Scenario 2: Follow-up on Sent Email
1. **User opens email from Sent folder**
2. **Clicks "Current open email"**
3. **Sees**: "Generating Follow-up..."
4. **Gets**: Follow-up email draft with "Follow-up: [Subject]"

### Scenario 3: Invalid Email Types
1. **User opens draft or deleted email**
2. **Clicks "Current open email"**
3. **Gets**: Appropriate error message

## Technical Implementation

### Enhanced Message Detection
```javascript
async function getCurrentOpenEmail() {
    // Get message from display tabs or mail tabs
    const result = await getMessageWithContext();
    
    if (result) {
        const { message, folderInfo } = result;
        return { message, folderInfo };
    }
    
    return null;
}
```

### Smart Reply Type Determination
```javascript
let replyType, replyContext;
if (folderInfo.isInbox) {
    replyType = 'reply';
    replyContext = 'inbox';
} else if (folderInfo.isSent) {
    replyType = 'followup';
    replyContext = 'sent';
} else if (folderInfo.isDrafts) {
    // Show error - cannot reply to drafts
} else if (folderInfo.isTrash) {
    // Show error - cannot reply to deleted emails
}
```

### Dynamic Subject Line Generation
```javascript
let subjectPrefix, recipientEmail;

if (replyType === 'followup') {
    subjectPrefix = 'Follow-up: ';
    recipientEmail = email.recipients.join(', '); // Original recipients
} else {
    subjectPrefix = 'Re: ';
    recipientEmail = email.author; // Original sender
}

const subjectLine = `${subjectPrefix}${email.subject}`;
```

## Benefits

### For Users
- **🎯 Context-Aware**: Automatically knows whether to reply or follow-up
- **⚡ Smart**: No need to manually choose reply type
- **🔄 Workflow-Friendly**: Works naturally with email reading patterns
- **💡 Intelligent**: Different prompts for different scenarios

### For Productivity
- **📈 Efficiency**: One-click appropriate response generation
- **🎨 Professional**: Follow-ups are polite and non-pushy
- **🔍 Accurate**: Correct recipients and subject lines
- **⚠️ Safe**: Prevents errors with drafts/deleted emails

## Testing Scenarios

### Test Case 1: Inbox Email Reply
1. Open any received email from Inbox
2. Click "Current open email"
3. Verify: "Generating Reply..." → Standard reply generated

### Test Case 2: Sent Email Follow-up
1. Open any sent email from Sent folder
2. Click "Current open email"  
3. Verify: "Generating Follow-up..." → Follow-up email generated

### Test Case 3: Draft Email Error
1. Open any draft email
2. Click "Current open email"
3. Verify: Error message about drafts

### Test Case 4: Custom Folder
1. Open email from custom folder (not Inbox/Sent)
2. Click "Current open email"
3. Verify: Treated as standard reply

## Console Output Examples

### Inbox Email:
```
📥 Email is from Inbox - generating standard reply
Generated prompt for reply: Generate a formal reply in English for the following email I received...
✅ Reply generated for current email: Meeting Request
```

### Sent Email:
```
📤 Email is from Sent folder - generating follow-up
Generated prompt for followup: Generate a formal follow-up email in English for the following email that I previously sent...
✅ Follow-up generated for current email: Project Proposal
```

This enhancement makes the add-on much smarter and more context-aware, providing exactly the right type of response based on the email's origin!
