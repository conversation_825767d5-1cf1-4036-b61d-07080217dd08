// Test script to debug email fetching functionality
// This can be run in the browser console when the popup is open

async function testEmailFetching() {
  console.log('Starting email fetch test...');
  
  try {
    // Test 1: Check if messenger API is available
    console.log('1. Testing messenger API availability...');
    if (typeof messenger === 'undefined') {
      console.error('messenger API is not available');
      return;
    }
    console.log('✓ messenger API is available');
    
    // Test 2: Check accounts
    console.log('2. Testing account access...');
    const accounts = await messenger.accounts.list();
    console.log(`✓ Found ${accounts.length} accounts:`, accounts.map(a => a.name));
    
    if (accounts.length === 0) {
      console.error('No accounts found');
      return;
    }
    
    // Test 3: Check folders
    console.log('3. Testing folder access...');
    for (const account of accounts) {
      console.log(`Account: ${account.name}`);
      console.log('Folders:', account.folders.map(f => `${f.name} (${f.type})`));
      
      // Find inbox
      const inbox = account.folders.find(f => f.type === 'inbox' || f.name.toLowerCase() === 'inbox');
      if (inbox) {
        console.log(`✓ Found inbox: ${inbox.name}`);
        
        // Test 4: List messages from inbox
        console.log('4. Testing message listing...');
        try {
          const messageList = await messenger.messages.list(inbox);
          console.log(`✓ Found ${messageList.messages.length} messages in inbox`);
          
          if (messageList.messages.length > 0) {
            const firstMessage = messageList.messages[0];
            console.log('First message:', {
              id: firstMessage.id,
              subject: firstMessage.subject,
              author: firstMessage.author,
              date: firstMessage.date
            });
          }
        } catch (listError) {
          console.error('Error listing messages:', listError);
        }
        
        // Test 5: Query messages
        console.log('5. Testing message query...');
        try {
          const queryResult = await messenger.messages.query({ folder: inbox });
          console.log(`✓ Query found ${queryResult.messages.length} messages`);
        } catch (queryError) {
          console.error('Error querying messages:', queryError);
        }
      } else {
        console.warn(`No inbox found for account ${account.name}`);
      }
    }
    
    console.log('Email fetch test completed');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testEmailFetching();
