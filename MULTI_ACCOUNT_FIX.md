# Multi-Account Identity Fix

## Problem Description
When using multiple email accounts in Thunderbird (e.g., user1@example.<NAME_EMAIL>), the AI Smart Reply add-on was always using the default account identity for generated replies, regardless of which account's email was selected. This caused:

1. **Wrong "From" field**: Replies always showed the default account email
2. **Wrong draft folder**: Drafts were saved to the default account instead of the correct account
3. **Identity mismatch**: Recipients would see replies from the wrong email address

## Solution Overview
Enhanced the add-on to automatically detect and use the correct email identity based on the selected email's account context.

## Technical Implementation

### 1. Identity Detection Logic

#### For Replies (Inbox emails):
```javascript
// Find which of our identities received the email
const allRecipients = [
    ...(email.recipients || []),
    ...(email.ccList || []),
    ...(email.bccList || [])
];

// Match recipient with our identities
for (const recipient of allRecipients) {
    const matchingIdentity = identities.find(identity => 
        identity.email.toLowerCase() === recipientEmail.toLowerCase()
    );
    if (matchingIdentity) {
        return matchingIdentity; // Use this identity for reply
    }
}
```

#### For Follow-ups (Sent emails):
```javascript
// For follow-ups, use the identity that sent the original email
targetEmail = email.author; // The sender of the original email
const matchingIdentity = identities.find(identity => 
    identity.email.toLowerCase() === targetEmail.toLowerCase()
);
```

#### Fallback - Account-based Detection:
```javascript
// If not found in recipients, determine from folder's account
const accounts = await messenger.accounts.list();
for (const account of accounts) {
    if (await isFolderInAccount(email.folder, account)) {
        const defaultIdentity = await messenger.identities.getDefault(account.id);
        return defaultIdentity;
    }
}
```

### 2. Enhanced Compose Operations

#### Direct Compose (Non-editor mode):
```javascript
const composeDetails = {
    to: recipientEmail,
    subject: subjectLine,
    plainTextBody: reply
};

// Add correct identity
if (correctIdentity) {
    composeDetails.identityId = correctIdentity.id;
}

await messenger.compose.beginNew(composeDetails);
```

#### Editor Mode:
```javascript
// Pass identity via URL parameters
const editorUrl = new URL(messenger.runtime.getURL('editor/editor.html'));
editorUrl.searchParams.set('to', recipientEmail);
editorUrl.searchParams.set('subject', subjectLine);
editorUrl.searchParams.set('body', reply);
if (correctIdentity) {
    editorUrl.searchParams.set('identityId', correctIdentity.id);
    editorUrl.searchParams.set('identityEmail', correctIdentity.email);
}
```

### 3. Editor Enhancement
Updated editor.js to handle identity information:

```javascript
// Extract identity from URL parameters
const identityId = urlParams.get('identityId');
const identityEmail = urlParams.get('identityEmail');

// Show identity indicator in editor
if (identityEmail) {
    const identityIndicator = document.createElement('div');
    identityIndicator.innerHTML = `<strong>From:</strong> ${identityEmail}`;
    editorForm.insertBefore(identityIndicator, editorForm.firstChild);
}

// Use correct identity when creating compose
const composeDetails = {
    to: toInput.value,
    subject: subjectInput.value,
    plainTextBody: bodyTextarea.value
};

if (identityId) {
    composeDetails.identityId = identityId;
}

await messenger.compose.beginNew(composeDetails);
```

## Key Functions Added

### `getCorrectIdentityForEmail(email, replyType, folderInfo)`
Main function that determines the correct identity to use:
- **Input**: Email object, reply type, folder information
- **Output**: Identity object or null (for default)
- **Logic**: Analyzes recipients, sender, and folder context

### `isFolderInAccount(folder, account)`
Helper to check if a folder belongs to a specific account:
- **Input**: Folder object, account object
- **Output**: Boolean indicating if folder belongs to account
- **Logic**: Compares folder IDs and paths with account folders

### `getAllAccountFolders(folders)`
Recursively gets all folders from an account:
- **Input**: Array of top-level folders
- **Output**: Flattened array of all folders (including subfolders)
- **Logic**: Recursive traversal of folder hierarchy

## User Experience Improvements

### Before Fix:
1. Select <NAME_EMAIL> account
2. Generate reply
3. **Problem**: Draft shows "From: <EMAIL>" (default account)
4. **Problem**: Draft <NAME_EMAIL> drafts folder

### After Fix:
1. Select <NAME_EMAIL> account
2. Generate reply
3. **✅ Correct**: Draft shows "From: <EMAIL>"
4. **✅ Correct**: Draft <NAME_EMAIL> drafts folder
5. **✅ Correct**: Recipients see reply from correct email address

## Console Output Examples

### Successful Identity Detection:
```
Determining correct identity for email...
Available identities: ["<EMAIL>", "<EMAIL>"]
Reply: Checking recipients to find our identity: ["<EMAIL>"]
Found matching identity in recipients: <EMAIL>
✅ Found correct identity: <EMAIL>
Using identity: <EMAIL>
Setting identity to: <EMAIL> (ID: identity123)
```

### Fallback to Account Default:
```
Determining correct identity for email...
Available identities: ["<EMAIL>", "<EMAIL>"]
Trying to determine identity from folder account...
Using default identity for account: <EMAIL>
✅ Found correct identity: <EMAIL>
```

### No Specific Identity Found:
```
Determining correct identity for email...
Available identities: ["<EMAIL>", "<EMAIL>"]
⚠️ No specific identity found, using default
Using identity: default
```

## Testing Scenarios

### Test Case 1: User2 Inbox Email Reply
1. **Setup**: Email <NAME_EMAIL> inbox
2. **Action**: Generate reply using "Current open email"
3. **Expected**: Reply draft shows "From: <EMAIL>"
4. **Expected**: Draft <NAME_EMAIL> drafts

### Test Case 2: User2 Sent Email Follow-up
1. **Setup**: Email <NAME_EMAIL> (in sent folder)
2. **Action**: Generate follow-up using "Current open email"
3. **Expected**: Follow-up draft shows "From: <EMAIL>"
4. **Expected**: Draft <NAME_EMAIL> drafts

### Test Case 3: Mixed Account Scenario
1. **Setup**: Multiple accounts configured
2. **Action**: Switch between emails from different accounts
3. **Expected**: Each reply uses the correct account identity

## Benefits

### For Users:
- **🎯 Correct Identity**: Always uses the right email address
- **📁 Proper Organization**: Drafts saved to correct account folders
- **👥 Professional**: Recipients see replies from expected email address
- **🔄 Seamless**: Works automatically without user intervention

### For Multi-Account Workflows:
- **🏢 Business/Personal**: Separate work and personal email accounts
- **🌐 Multiple Domains**: Different email domains handled correctly
- **👤 Role-based**: Different roles/departments with separate emails
- **🔒 Security**: Prevents accidental cross-account replies

This fix ensures that the AI Smart Reply add-on respects Thunderbird's multi-account setup and maintains proper email identity context!
