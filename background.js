let failedRequests = new Map();

// On first install, open the options page to configure the add-on.
messenger.runtime.onInstalled.addListener(details => {
  if (details.reason === 'install') {
    messenger.runtime.openOptionsPage();
  }
});

async function generateReply(email, tone, language) {
    try {
        console.log('Starting reply generation for email:', email.subject);
        console.log('Email content length:', email.content ? email.content.length : 'No content');

        const config = await messenger.storage.local.get(['llmProvider', 'apiKey', 'llmModel', 'customProviderUrl', 'editDraftsBeforeSaving']);
        console.log('Configuration loaded:', {
            provider: config.llmProvider,
            hasApiKey: !!config.apiKey,
            model: config.llmModel
        });

        if (!config.apiKey) {
            console.error('No API key configured');
            messenger.notifications.create({
                "type": "basic",
                "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
                "title": "Configuration Needed",
                "message": "Please configure the AI provider and API key in the add-on options."
            });
            messenger.runtime.openOptionsPage();
            return;
        }

        if (!email.content || email.content.trim() === '') {
            console.error('No email content available for reply generation');
            messenger.notifications.create({
                "type": "basic",
                "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
                "title": "Error",
                "message": "No email content available to generate a reply."
            });
            return;
        }

        console.log('Calling LLM API...');
        const reply = await callLLMApi(config, email.content, tone, language);
        console.log('Reply generated, length:', reply.length);

        if (config.editDraftsBeforeSaving) {
            await messenger.tabs.create({
                url: messenger.runtime.getURL(`editor/editor.html?to=${encodeURIComponent(email.author)}&subject=${encodeURIComponent(`Re: ${email.subject}`)}&body=${encodeURIComponent(reply)}`)
            });
        } else {
            await messenger.compose.beginNew({
                to: email.author,
                subject: `Re: ${email.subject}`,
                plainTextBody: reply
            });
        }

    } catch (error) {
        console.error("Error generating reply:", error);
        const notificationId = await messenger.notifications.create({
            "type": "basic",
            "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
            "title": "Error",
            "message": `Failed to generate reply for \"${email.subject}\".`,
            "buttons": [
                { "title": "Retry" }
            ]
        });
        failedRequests.set(notificationId, { email, tone, language });
    }
}

// Listen for messages from the popup
messenger.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
    if (request.action === "generateReply") {
        const { email, tone, language } = request;
        await generateReply(email, tone, language);
    }
});

messenger.notifications.onButtonClicked.addListener(async (notificationId, buttonIndex) => {
    if (failedRequests.has(notificationId)) {
        const request = failedRequests.get(notificationId);
        failedRequests.delete(notificationId); // Remove from map
        
        // Retry the action
        await generateReply(request.email, request.tone, request.language);
        
        messenger.notifications.clear(notificationId); // Clear the original notification
    }
});

messenger.notifications.onClosed.addListener((notificationId) => {
    if (failedRequests.has(notificationId)) {
        failedRequests.delete(notificationId);
    }
});


async function callLLMApi(config, emailContent, tone, language) {
    let endpoint = '';
    let headers = {};
    let body = {};

    const prompt = `Generate a ${tone} reply in ${language} for the following email:\n\n${emailContent}`;

    switch (config.llmProvider) {
        case 'openai':
            endpoint = 'https://api.openai.com/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'anthropic':
            endpoint = 'https://api.anthropic.com/v1/messages';
            headers = {
                'Content-Type': 'application/json',
                'x-api-key': config.apiKey,
                'anthropic-version': '2023-06-01'
            };
            body = {
                model: config.llmModel || 'claude-2.1',
                max_tokens: 1024,
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'mistral':
            endpoint = 'https://api.mistral.ai/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'mistral-tiny',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'gemini':
            endpoint = `https://generativelanguage.googleapis.com/v1beta/models/${config.llmModel || 'gemini-pro'}:generateContent?key=${config.apiKey}`;
            headers = {
                'Content-Type': 'application/json'
            };
            body = {
                contents: [{ parts: [{ text: prompt }] }]
            };
            break;
        case 'deepseek':
            endpoint = 'https://api.deepseek.com/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'deepseek-chat',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'openrouter':
            endpoint = 'https://openrouter.ai/api/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'openrouter/auto',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'custom':
            endpoint = config.customProviderUrl;
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel,
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        default:
            throw new Error('Invalid LLM provider selected.');
    }

    const response = await fetch(endpoint, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
    }

    const data = await response.json();

    switch (config.llmProvider) {
        case 'openai':
        case 'mistral':
            return data.choices[0].message.content.trim();
        case 'anthropic':
            return data.content[0].text.trim();
        case 'gemini':
            return data.candidates[0].content.parts[0].text.trim();
        case 'deepseek':
        case 'openrouter':
            return data.choices[0].message.content.trim();
        case 'custom':
            // This might need adjustment based on the custom API's response structure
            return data.choices[0].message.content.trim(); 
        default:
            throw new Error('Invalid LLM provider selected.');
    }
}