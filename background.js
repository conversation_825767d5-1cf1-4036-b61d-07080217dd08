let failedRequests = new Map();

// On first install, open the options page to configure the add-on.
messenger.runtime.onInstalled.addListener(details => {
  if (details.reason === 'install') {
    messenger.runtime.openOptionsPage();
  }
});

// Helper function to determine the correct identity for email replies
async function getCorrectIdentityForEmail(email, replyType, folderInfo) {
    try {
        console.log('Determining correct identity for email...');
        console.log('Email folder info:', email.folder);

        // Method 1: Try to get the account from the email's folder accountId
        if (email.folder && email.folder.accountId) {
            console.log('Email folder has accountId:', email.folder.accountId);
            try {
                const account = await messenger.accounts.get(email.folder.accountId);
                if (account) {
                    console.log('Found account:', account.name);
                    const defaultIdentity = await messenger.identities.getDefault(account.id);
                    if (defaultIdentity) {
                        console.log('✅ Method 1 success - Using account default identity:', defaultIdentity.email);
                        return defaultIdentity;
                    }
                }
            } catch (accountError) {
                console.log('Method 1 failed - Error getting account by ID:', accountError);
            }
        }

        // Method 2: Try to extract account info from folder path/name
        if (email.folder && email.folder.path) {
            console.log('Trying to extract account from folder path:', email.folder.path);
            try {
                const accounts = await messenger.accounts.list();

                // Look for account name in folder path
                for (const account of accounts) {
                    if (email.folder.path.includes(account.name) ||
                        email.folder.path.includes(account.id) ||
                        (account.identities && account.identities.length > 0 &&
                         email.folder.path.includes(account.identities[0].email))) {

                        console.log('Found account match in path:', account.name);
                        const defaultIdentity = await messenger.identities.getDefault(account.id);
                        if (defaultIdentity) {
                            console.log('✅ Method 2 success - Using account default identity:', defaultIdentity.email);
                            return defaultIdentity;
                        }
                    }
                }
            } catch (pathError) {
                console.log('Method 2 failed - Error extracting from path:', pathError);
            }
        }

        // Fallback: Get all identities and try to match
        let identities = [];
        try {
            identities = await messenger.identities.list();
            console.log('Available identities:', identities.map(i => i.email));
        } catch (identitiesError) {
            console.error('Error getting identities list:', identitiesError);
            return null; // Use default
        }

        // Alternative approach: Try to determine from folder's account directly
        if (email.folder) {
            console.log('Trying to determine identity from folder account...');

            try {
                // Get all accounts and find which one contains this folder
                const accounts = await messenger.accounts.list();
                console.log('Available accounts:', accounts.map(a => a.name));

                for (const account of accounts) {
                    // Check if this folder belongs to this account
                    if (await isFolderInAccount(email.folder, account)) {
                        console.log('Found folder belongs to account:', account.name);

                        // Get the default identity for this account
                        const defaultIdentity = await messenger.identities.getDefault(account.id);
                        if (defaultIdentity) {
                            console.log('✅ Using account default identity:', defaultIdentity.email);
                            return defaultIdentity;
                        }
                    }
                }
            } catch (accountError) {
                console.error('Error determining account from folder:', accountError);
            }
        }

        // Fallback: Try recipient/sender matching
        let targetEmail = null;

        if (replyType === 'followup') {
            // For follow-ups (sent emails), use the identity that sent the original email
            targetEmail = email.author;
            console.log('Follow-up: Looking for identity that matches sender:', targetEmail);
        } else {
            // For replies (received emails), find the identity that received the email
            const allRecipients = [
                ...(email.recipients || []),
                ...(email.ccList || []),
                ...(email.bccList || [])
            ];

            console.log('Reply: Checking recipients to find our identity:', allRecipients);

            // Find which of our identities is in the recipient list
            for (const recipient of allRecipients) {
                const recipientEmail = typeof recipient === 'string' ? recipient : recipient.email || recipient.name || recipient;
                const matchingIdentity = identities.find(identity =>
                    identity.email.toLowerCase() === recipientEmail.toLowerCase()
                );

                if (matchingIdentity) {
                    targetEmail = matchingIdentity.email;
                    console.log('Found matching identity in recipients:', targetEmail);
                    break;
                }
            }
        }

        // Find the matching identity
        if (targetEmail) {
            const matchingIdentity = identities.find(identity =>
                identity.email.toLowerCase() === targetEmail.toLowerCase()
            );

            if (matchingIdentity) {
                console.log('✅ Found correct identity:', matchingIdentity.email);
                return matchingIdentity;
            }
        }

        console.log('⚠️ No specific identity found, using default');
        return null; // Will use default identity

    } catch (error) {
        console.error('Error determining correct identity:', error);
        return null; // Will use default identity
    }
}

// Helper function to check if a folder belongs to an account
async function isFolderInAccount(folder, account) {
    try {
        console.log(`Checking if folder "${folder.name}" belongs to account "${account.name}"`);

        // Method 1: Check if folder has accountId that matches
        if (folder.accountId && folder.accountId === account.id) {
            console.log('✅ Folder accountId matches account ID');
            return true;
        }

        // Method 2: Check folder path/name patterns
        if (folder.path && folder.path.includes(account.name)) {
            console.log('✅ Folder path contains account name');
            return true;
        }

        // Method 3: Compare with account folders
        const accountFolders = await getAllAccountFolders(account.folders);
        const isMatch = accountFolders.some(f => {
            const idMatch = f.id === folder.id;
            const pathMatch = f.path === folder.path;
            const nameMatch = f.name === folder.name && f.type === folder.type;

            if (idMatch || pathMatch || nameMatch) {
                console.log('✅ Found folder match:', { idMatch, pathMatch, nameMatch });
                return true;
            }
            return false;
        });

        if (!isMatch) {
            console.log('❌ Folder does not belong to this account');
        }

        return isMatch;
    } catch (error) {
        console.error('Error checking folder account:', error);
        return false;
    }
}

// Helper function to get all folders from an account recursively
function getAllAccountFolders(folders) {
    let allFolders = [];
    for (const folder of folders) {
        allFolders.push(folder);
        if (folder.subFolders && folder.subFolders.length > 0) {
            allFolders = allFolders.concat(getAllAccountFolders(folder.subFolders));
        }
    }
    return allFolders;
}

async function generateReply(email, tone, language, replyType = 'reply', replyContext = 'inbox', folderInfo = null) {
    try {
        console.log('Starting reply generation for email:', email.subject);
        console.log('Email content length:', email.content ? email.content.length : 'No content');
        console.log('Reply type:', replyType, 'Context:', replyContext);
        if (folderInfo) {
            console.log('Folder info:', folderInfo.name, 'Type:', folderInfo.type);
        }

        const config = await messenger.storage.local.get(['llmProvider', 'apiKey', 'llmModel', 'customProviderUrl', 'editDraftsBeforeSaving']);
        console.log('Configuration loaded:', {
            provider: config.llmProvider,
            hasApiKey: !!config.apiKey,
            model: config.llmModel
        });

        if (!config.apiKey) {
            console.error('No API key configured');
            messenger.notifications.create({
                "type": "basic",
                "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
                "title": "Configuration Needed",
                "message": "Please configure the AI provider and API key in the add-on options."
            });
            messenger.runtime.openOptionsPage();
            return { success: false, error: 'No API key configured' };
        }

        if (!email.content || email.content.trim() === '') {
            console.error('No email content available for reply generation');
            messenger.notifications.create({
                "type": "basic",
                "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
                "title": "Error",
                "message": "No email content available to generate a reply."
            });
            return { success: false, error: 'No email content available' };
        }

        console.log('Calling LLM API...');
        const reply = await callLLMApi(config, email.content, tone, language, replyType, email);
        console.log('Reply generated, length:', reply.length);

        // Determine the correct identity to use for the reply
        const correctIdentity = await getCorrectIdentityForEmail(email, replyType, folderInfo);
        console.log('Using identity:', correctIdentity ? correctIdentity.email : 'default');

        // Debug: Log email and folder information
        console.log('Email debug info:', {
            subject: email.subject,
            author: email.author,
            recipients: email.recipients,
            folder: email.folder ? {
                name: email.folder.name,
                type: email.folder.type,
                accountId: email.folder.accountId,
                path: email.folder.path
            } : 'No folder info'
        });

        // Generate appropriate subject line based on reply type
        let subjectPrefix, recipientEmail;

        if (replyType === 'followup') {
            subjectPrefix = 'Follow-up: ';
            // For follow-up emails, we need to determine the recipient
            // For sent emails, the original recipients become the new recipients
            recipientEmail = email.recipients && email.recipients.length > 0
                ? email.recipients.map(r => typeof r === 'string' ? r : r.email || r.name || r).join(', ')
                : email.author; // Fallback to author if no recipients found
        } else {
            subjectPrefix = 'Re: ';
            recipientEmail = email.author;
        }

        const subjectLine = email.subject.startsWith('Re: ') || email.subject.startsWith('Follow-up: ')
            ? email.subject
            : `${subjectPrefix}${email.subject}`;

        // Prepare compose details with correct identity
        const composeDetails = {
            to: recipientEmail,
            subject: subjectLine,
            plainTextBody: reply
        };

        // Add identity if we found a specific one
        if (correctIdentity) {
            composeDetails.identityId = correctIdentity.id;
            console.log(`Setting identity to: ${correctIdentity.email} (ID: ${correctIdentity.id})`);
        }

        if (config.editDraftsBeforeSaving) {
            // For editor mode, we need to pass the identity information via URL
            const editorUrl = new URL(messenger.runtime.getURL('editor/editor.html'));
            editorUrl.searchParams.set('to', recipientEmail);
            editorUrl.searchParams.set('subject', subjectLine);
            editorUrl.searchParams.set('body', reply);
            if (correctIdentity) {
                editorUrl.searchParams.set('identityId', correctIdentity.id);
                editorUrl.searchParams.set('identityEmail', correctIdentity.email);
            }

            await messenger.tabs.create({
                url: editorUrl.toString()
            });
        } else {
            await messenger.compose.beginNew(composeDetails);
        }

        console.log(`✅ Successfully generated reply for: ${email.subject}`);
        return { success: true, message: 'Reply generated successfully' };

    } catch (error) {
        console.error("Error generating reply:", error);

        // Still show notification for user awareness, but don't throw
        const notificationId = await messenger.notifications.create({
            "type": "basic",
            "iconUrl": messenger.runtime.getURL("icons/icon-48.png"),
            "title": "Error",
            "message": `Failed to generate reply for \"${email.subject}\". Check console for details.`
        });
        failedRequests.set(notificationId, { email, tone, language });

        // Return error instead of throwing
        return { success: false, error: error.message };
    }
}

// Listen for messages from the popup
messenger.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
    if (request.action === "generateReply") {
        const { email, tone, language, replyType, replyContext, folderInfo } = request;
        try {
            const result = await generateReply(email, tone, language, replyType, replyContext, folderInfo);
            return { success: true, result: result };
        } catch (error) {
            console.error('Error in message handler:', error);
            return { success: false, error: error.message };
        }
    }
});

messenger.notifications.onButtonClicked.addListener(async (notificationId, buttonIndex) => {
    if (failedRequests.has(notificationId)) {
        const request = failedRequests.get(notificationId);
        failedRequests.delete(notificationId); // Remove from map
        
        // Retry the action
        await generateReply(request.email, request.tone, request.language);
        
        messenger.notifications.clear(notificationId); // Clear the original notification
    }
});

messenger.notifications.onClosed.addListener((notificationId) => {
    if (failedRequests.has(notificationId)) {
        failedRequests.delete(notificationId);
    }
});


async function callLLMApi(config, emailContent, tone, language, replyType = 'reply', emailInfo = null) {
    let endpoint = '';
    let headers = {};
    let body = {};

    // Generate different prompts based on reply type
    let prompt;
    if (replyType === 'followup') {
        prompt = `Generate a ${tone} follow-up email in ${language} for the following email that I previously sent. The follow-up should:
- Check on the status or response to my original email
- Be polite and professional
- Not be pushy or aggressive
- Include a gentle reminder if appropriate
- Offer additional assistance or clarification if needed

Original email I sent:
${emailContent}

Generate a follow-up email:`;
    } else {
        prompt = `Generate a ${tone} reply in ${language} for the following email I received:

${emailContent}

Generate a professional reply:`;
    }

    console.log('Generated prompt for', replyType, ':', prompt.substring(0, 200) + '...');

    switch (config.llmProvider) {
        case 'openai':
            endpoint = 'https://api.openai.com/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'anthropic':
            endpoint = 'https://api.anthropic.com/v1/messages';
            headers = {
                'Content-Type': 'application/json',
                'x-api-key': config.apiKey,
                'anthropic-version': '2023-06-01'
            };
            body = {
                model: config.llmModel || 'claude-2.1',
                max_tokens: 1024,
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'mistral':
            endpoint = 'https://api.mistral.ai/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'mistral-tiny',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'gemini':
            endpoint = `https://generativelanguage.googleapis.com/v1beta/models/${config.llmModel || 'gemini-pro'}:generateContent?key=${config.apiKey}`;
            headers = {
                'Content-Type': 'application/json'
            };
            body = {
                contents: [{ parts: [{ text: prompt }] }]
            };
            break;
        case 'deepseek':
            endpoint = 'https://api.deepseek.com/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'deepseek-chat',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'openrouter':
            endpoint = 'https://openrouter.ai/api/v1/chat/completions';
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel || 'openrouter/auto',
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        case 'custom':
            endpoint = config.customProviderUrl;
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            };
            body = {
                model: config.llmModel,
                messages: [{ role: 'user', content: prompt }]
            };
            break;
        default:
            throw new Error('Invalid LLM provider selected.');
    }

    const response = await fetch(endpoint, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
    }

    const data = await response.json();

    switch (config.llmProvider) {
        case 'openai':
        case 'mistral':
            return data.choices[0].message.content.trim();
        case 'anthropic':
            return data.content[0].text.trim();
        case 'gemini':
            return data.candidates[0].content.parts[0].text.trim();
        case 'deepseek':
        case 'openrouter':
            return data.choices[0].message.content.trim();
        case 'custom':
            // This might need adjustment based on the custom API's response structure
            return data.choices[0].message.content.trim(); 
        default:
            throw new Error('Invalid LLM provider selected.');
    }
}