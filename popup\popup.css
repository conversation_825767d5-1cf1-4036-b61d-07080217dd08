body {
  font-family: sans-serif;
  font-size: 12px;
  color: #333;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

.container {
  width: 400px;
  padding: 10px;
}

h1 {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.section {
  margin-bottom: 10px;
}

.section h2 {
  font-size: 11px;
  font-weight: bold;
  color: #555;
  margin: 0 0 5px 0;
}

.form-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-size: 11px;
  margin-bottom: 2px;
}

input[type="text"] {
  width: 100%;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-sizing: border-box;
}

.form-row-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  margin-top: 5px;
}

button {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
  cursor: pointer;
}

.draft-btn {
  background-color: #00c853;
  color: #fff;
  border-color: #00c853;
}

.form-row-checkbox {
  display: flex;
  gap: 10px;
  align-items: center;
  margin: 5px 0;
}

.form-row-checkbox label {
  font-size: 11px;
}

#sender-input {
  width: 100%;
  margin-bottom: 5px;
}

.email-list-container {
  height: 100px;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 5px;
}

#email-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

#email-list li {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

#email-list input[type="checkbox"] {
  margin-right: 5px;
}

.progress-container {
  width: 100%;
  height: 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

#progress-bar {
  width: 100%;
  height: 100%;
  background-color: #e9ecef;
}