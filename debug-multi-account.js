// Debug script for multi-account issues
// Run this in the browser console when the popup is open

async function debugMultiAccount() {
    console.log('=== Multi-Account Debug Information ===');
    
    try {
        // 1. Check available accounts
        console.log('1. Checking available accounts...');
        const accounts = await messenger.accounts.list();
        console.log('Available accounts:', accounts.map(a => ({
            id: a.id,
            name: a.name,
            type: a.type,
            identities: a.identities ? a.identities.map(i => i.email) : 'No identities'
        })));
        
        // 2. Check available identities
        console.log('2. Checking available identities...');
        try {
            const identities = await messenger.identities.list();
            console.log('Available identities:', identities.map(i => ({
                id: i.id,
                email: i.email,
                name: i.name,
                accountId: i.accountId
            })));
        } catch (identitiesError) {
            console.error('Error getting identities:', identitiesError);
        }
        
        // 3. Check current open email
        console.log('3. Checking current open email...');
        try {
            const tabs = await messenger.tabs.query({ active: true, currentWindow: true });
            
            for (const tab of tabs) {
                try {
                    const displayedMessages = await messenger.messageDisplay.getDisplayedMessages(tab.id);
                    
                    if (displayedMessages && displayedMessages.length > 0) {
                        const message = displayedMessages[0];
                        console.log('Current email info:', {
                            id: message.id,
                            subject: message.subject,
                            author: message.author,
                            recipients: message.recipients,
                            folder: message.folder ? {
                                id: message.folder.id,
                                name: message.folder.name,
                                type: message.folder.type,
                                accountId: message.folder.accountId,
                                path: message.folder.path
                            } : 'No folder info'
                        });
                        
                        // 4. Try to determine correct identity for this email
                        console.log('4. Determining correct identity...');
                        
                        if (message.folder && message.folder.accountId) {
                            try {
                                const account = await messenger.accounts.get(message.folder.accountId);
                                console.log('Email belongs to account:', account.name);
                                
                                const defaultIdentity = await messenger.identities.getDefault(account.id);
                                console.log('Default identity for this account:', defaultIdentity ? defaultIdentity.email : 'None found');
                            } catch (accountError) {
                                console.error('Error getting account:', accountError);
                            }
                        } else {
                            console.log('No accountId in folder, trying alternative methods...');
                            
                            // Try to match folder with accounts
                            for (const account of accounts) {
                                console.log(`Checking if folder belongs to account: ${account.name}`);
                                
                                // Check if folder name/path contains account info
                                if (message.folder && 
                                    (message.folder.path?.includes(account.name) || 
                                     message.folder.path?.includes(account.id))) {
                                    console.log('✅ Found potential match based on path');
                                    
                                    const defaultIdentity = await messenger.identities.getDefault(account.id);
                                    console.log('Would use identity:', defaultIdentity ? defaultIdentity.email : 'None');
                                }
                            }
                        }
                        
                        break;
                    }
                } catch (tabError) {
                    console.log('Tab', tab.id, 'is not a message display tab');
                }
            }
        } catch (emailError) {
            console.error('Error getting current email:', emailError);
        }
        
        console.log('=== Debug Complete ===');
        
    } catch (error) {
        console.error('Debug script error:', error);
    }
}

// Run the debug
debugMultiAccount();
