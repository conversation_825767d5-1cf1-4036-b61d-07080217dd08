document.addEventListener('DOMContentLoaded', () => {
  const editorForm = document.getElementById('editor-form');
  const toInput = document.getElementById('to');
  const subjectInput = document.getElementById('subject');
  const bodyTextarea = document.getElementById('body');

  const urlParams = new URLSearchParams(window.location.search);
  const to = urlParams.get('to');
  const subject = urlParams.get('subject');
  const body = urlParams.get('body');

  toInput.value = decodeURIComponent(to);
  subjectInput.value = decodeURIComponent(subject);
  bodyTextarea.value = decodeURIComponent(body);

  editorForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    await browser.compose.beginNew({
        to: toInput.value,
        subject: subjectInput.value,
        plainTextBody: bodyTextarea.value
    });

    // Close the editor tab
    const tab = await browser.tabs.getCurrent();
    browser.tabs.remove(tab.id);
  });
});