document.addEventListener('DOMContentLoaded', () => {
  const editorForm = document.getElementById('editor-form');
  const toInput = document.getElementById('to');
  const subjectInput = document.getElementById('subject');
  const bodyTextarea = document.getElementById('body');

  const urlParams = new URLSearchParams(window.location.search);
  const to = urlParams.get('to');
  const subject = urlParams.get('subject');
  const body = urlParams.get('body');
  const identityId = urlParams.get('identityId');
  const identityEmail = urlParams.get('identityEmail');

  // Handle null values to prevent errors
  toInput.value = to ? decodeURIComponent(to) : '';
  subjectInput.value = subject ? decodeURIComponent(subject) : '';
  bodyTextarea.value = body ? decodeURIComponent(body) : '';

  // Display identity information if available
  if (identityEmail) {
    console.log('Using identity for editor:', identityEmail);

    // Add identity indicator to the editor
    const identityIndicator = document.createElement('div');
    identityIndicator.style.cssText = 'background: #e8f5e8; padding: 8px; margin-bottom: 10px; border-radius: 4px; font-size: 12px;';
    identityIndicator.innerHTML = `<strong>From:</strong> ${decodeURIComponent(identityEmail)}`;
    editorForm.insertBefore(identityIndicator, editorForm.firstChild);
  }

  editorForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    try {
      // Prepare compose details
      const composeDetails = {
          to: toInput.value,
          subject: subjectInput.value,
          plainTextBody: bodyTextarea.value
      };

      // Add identity if specified
      if (identityId) {
        composeDetails.identityId = identityId;
        console.log('Creating compose with identity ID:', identityId);
      }

      await messenger.compose.beginNew(composeDetails);

      // Close the editor tab
      const tab = await messenger.tabs.getCurrent();
      messenger.tabs.remove(tab.id);
    } catch (error) {
      console.error('Error creating compose window:', error);
      alert('Error creating email draft: ' + error.message);
    }
  });
});