document.addEventListener('DOMContentLoaded', () => {
  const editorForm = document.getElementById('editor-form');
  const toInput = document.getElementById('to');
  const subjectInput = document.getElementById('subject');
  const bodyTextarea = document.getElementById('body');

  const urlParams = new URLSearchParams(window.location.search);
  const to = urlParams.get('to');
  const subject = urlParams.get('subject');
  const body = urlParams.get('body');

  // Handle null values to prevent errors
  toInput.value = to ? decodeURIComponent(to) : '';
  subjectInput.value = subject ? decodeURIComponent(subject) : '';
  bodyTextarea.value = body ? decodeURIComponent(body) : '';

  editorForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    try {
      await messenger.compose.beginNew({
          to: toInput.value,
          subject: subjectInput.value,
          plainTextBody: bodyTextarea.value
      });

      // Close the editor tab
      const tab = await messenger.tabs.getCurrent();
      messenger.tabs.remove(tab.id);
    } catch (error) {
      console.error('Error creating compose window:', error);
      alert('Error creating email draft: ' + error.message);
    }
  });
});